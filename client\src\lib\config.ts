// API Configuration for different environments
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api';

// Environment detection
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;

// Log configuration in development
if (isDevelopment) {
  console.log('🔧 API Configuration:', {
    API_BASE_URL,
    environment: isDevelopment ? 'development' : 'production',
    viteEnv: import.meta.env.VITE_API_BASE_URL ? 'configured' : 'using default'
  });
}

