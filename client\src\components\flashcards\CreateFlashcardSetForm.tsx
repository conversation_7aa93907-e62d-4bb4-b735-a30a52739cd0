import React, { useState, FormEvent } from "react";
// Removed Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";

interface CreateFlashcardSetFormProps {
  studyDocumentId?: string; // Optional: if creating a set linked to a document
  onSetCreated: (setId: string, setName: string) => void; // Callback after successful creation
}

export const CreateFlashcardSetForm: React.FC<CreateFlashcardSetFormProps> = ({
  studyDocumentId,
  onSetCreated,
}) => {
  const { user } = useAuth();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!user) {
      setError("You must be logged in.");
      return;
    }
    if (!name.trim()) {
      setError("Set name is required.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Create flashcard set via backend API
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/flashcard-sets', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: name,
          description: description,
          study_document_id: studyDocumentId,
          flashcards: []
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create flashcard set');
      }

      const data = await response.json();

      const createdSetName = name; // Store the name before resetting
      setName("");
      setDescription("");
      alert("Flashcard set created successfully!");
      if (data?.id) {
        onSetCreated(data.id, createdSetName);
      }
    } catch (err: any) {
      setError(err.message || "Failed to create flashcard set.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 bg-slate-800 border border-slate-700 shadow-md rounded-lg mt-5">
      <h3 className="text-lg font-semibold mb-3 text-purple-400">
        Create New Flashcard Set
      </h3>
      {error && <p className="text-red-400 text-sm mb-2">{error}</p>}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="setName"
            className="block text-sm font-medium text-slate-300"
          >
            Set Name*
          </label>
          <input
            type="text"
            id="setName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            className="mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500"
          />
        </div>
        <div>
          <label
            htmlFor="setDescription"
            className="block text-sm font-medium text-slate-300"
          >
            Description (Optional)
          </label>
          <textarea
            id="setDescription"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            rows={3}
            className="mt-1 block w-full px-3 py-2 border !border-slate-700 !bg-slate-900 !text-slate-200 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 sm:text-sm placeholder:text-slate-500"
          />
        </div>
        {studyDocumentId && (
          <p className="text-sm text-slate-400">
            Linking to document ID: {studyDocumentId}
          </p>
        )}
        <button
          type="submit"
          disabled={loading}
          className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-purple-500 disabled:opacity-50 disabled:bg-slate-500"
        >
          {loading ? "Creating..." : "Create Set"}
        </button>
      </form>
    </div>
  );
};
