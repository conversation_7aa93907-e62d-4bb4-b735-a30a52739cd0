import React from "react";
import { useQuery } from "@tanstack/react-query";
import { Link, useParams, useLocation } from "wouter";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { AlertTriangle, CheckCircle } from "lucide-react";
import FlashcardViewer from "./FlashcardViewer";
// Removed direct Supabase import - using backend API endpoints
import { Skeleton } from "@/components/ui/skeleton";

const FlashcardReviewSection: React.FC = () => {
  const params = useParams<{ deckId: string }>();
  const [, navigate] = useLocation();
  const deckId = params?.deckId;

  const { data: deck, isLoading: isDeckLoading } = useQuery({
    queryKey: [`/api/flashcard-sets/${deckId}`],
    queryFn: async () => {
      if (!deckId) return null;

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(`/api/flashcard-sets/${deckId}`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null; // Deck not found
        }
        throw new Error(`Failed to fetch flashcard set: ${response.statusText}`);
      }

      return response.json();
    },
    enabled: !!deckId,
  });

  const { data: dueCards = [], isLoading: isCardsLoading } = useQuery({
    queryKey: [`/api/flashcard-sets/${deckId}/due`],
    queryFn: async () => {
      if (!deckId) return [];

      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error("Authentication required");
      }

      const response = await fetch(`/api/flashcard-sets/${deckId}/due`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return []; // No due cards or deck not found
        }
        throw new Error(`Failed to fetch due flashcards: ${response.statusText}`);
      }

      const cards = await response.json();
      console.log("🔍 Retrieved due flashcards for deck", deckId, ":", cards);
      return cards;
    },
    enabled: !!deckId,
  });

  const isLoading = isDeckLoading || isCardsLoading;

  if (isLoading) {
    return (
      <section className="mb-8 p-4 md:p-6">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-8 w-48 bg-slate-700" />
          <div className="space-x-2">
            <Skeleton className="h-9 w-24 inline-block bg-slate-700" />
            <Skeleton className="h-9 w-24 inline-block bg-slate-700" />
          </div>
        </div>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="mb-4">
              <Skeleton className="h-6 w-3/4 mb-1 bg-slate-700" />
              <Skeleton className="h-4 w-1/2 bg-slate-700" />
            </div>

            <div className="max-w-lg mx-auto mb-6">
              <Skeleton className="h-64 w-full rounded-xl bg-slate-700" />
              <div className="flex justify-center mt-4 space-x-2">
                <Skeleton className="h-10 w-24 bg-slate-700" />
                <Skeleton className="h-10 w-24 bg-slate-700" />
                <Skeleton className="h-10 w-24 bg-slate-700" />
              </div>
            </div>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (!deck) {
    return (
      <section className="mb-8 p-4 md:p-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2 text-purple-400">
              Deck Not Found
            </h3>
            <p className="text-slate-300 mb-4">
              The flashcard deck you're looking for doesn't exist.
            </p>
            <Link href="/">
              <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                Return to Dashboard
              </Button>
            </Link>
          </CardContent>
        </Card>
      </section>
    );
  }

  if (dueCards.length === 0) {
    return (
      <section className="mb-8 p-4 md:p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium text-purple-400">{deck.name}</h2>
          <div className="space-x-2">
            <Button
              variant="outline"
              onClick={() => navigate("/")}
              className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300"
            >
              Back to Dashboard
            </Button>
          </div>
        </div>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2 text-purple-400">
              All Caught Up!
            </h3>
            <p className="text-slate-300 mb-4">
              You've reviewed all the flashcards due for today. Check back
              tomorrow for more spaced repetition reviews.
            </p>
            <Button
              onClick={() => navigate("/")}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              Return to Dashboard
            </Button>
          </CardContent>
        </Card>
      </section>
    );
  }

  return (
    <section className="mb-8 p-4 md:p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-medium text-purple-400">
          Reviewing: {deck.name}
        </h2>
        <div className="space-x-2">
          <Button
            variant="outline"
            onClick={() => navigate("/")}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300"
          >
            <span>Back</span>
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/flashcards/edit/" + deckId)}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-purple-300"
          >
            <span>Edit Cards</span>
          </Button>
        </div>
      </div>

      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="mb-4">
            <h3 className="text-lg font-medium text-slate-100 mb-1">
              {deck.name}
            </h3>
            <p className="text-slate-300 text-sm">
              Review session - {dueCards.length} card
              {dueCards.length !== 1 ? "s" : ""} due today
            </p>
          </div>

          <FlashcardViewer flashcards={dueCards} deckId={deck.id} enableSRS />
        </CardContent>
      </Card>
    </section>
  );
};

export default FlashcardReviewSection;
