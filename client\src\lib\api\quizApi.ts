export interface SRSUpdateData {
  srs_level?: number;
  due_at?: string;
  last_reviewed_at?: string;
  srs_interval?: number;
  srs_ease_factor?: number;
  srs_repetitions?: number;
  srs_correct_streak?: number;
}

export class QuizAPI {
  private static async getAuthHeaders(): Promise<{ Authorization: string }> {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      throw new Error("No authentication token available");
    }
    return {
      Authorization: `Bearer ${token}`,
    };
  }

  static async updateQuestionSRS(
    questionId: string,
    srsData: SRSUpdateData
  ): Promise<void> {
    const headers = await this.getAuthHeaders();

    const response = await fetch(`/api/quizzes/questions/${questionId}/srs`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        ...headers,
      },
      body: JSON.stringify(srsData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `Failed to update SRS data: ${response.statusText}`
      );
    }
  }
}
