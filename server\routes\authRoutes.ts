import express, { Request, Response } from "express";
import { supabaseClient } from "../middleware/supabaseMiddleware";

const router = express.Router();

// POST /api/auth/login
router.post("/login", async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        error: "Email and password are required",
      });
    }

    const { data, error } = await supabaseClient.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error("Login error:", error);
      return res.status(401).json({
        error: error.message,
      });
    }

    if (!data.session || !data.user) {
      return res.status(401).json({
        error: "Authentication failed",
      });
    }

    res.json({
      success: true,
      session: data.session,
      user: data.user,
    });
  } catch (error: any) {
    console.error("Login error:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// POST /api/auth/signup
router.post("/signup", async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        error: "Email and password are required",
      });
    }

    // Determine the correct redirect URL based on environment
    const isProduction = process.env.NODE_ENV === 'production';
    const redirectTo = isProduction
      ? 'https://chewy-ai.replit.app'
      : 'http://localhost:3000';

    const { data, error } = await supabaseClient.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectTo,
        data: {
          // Add any additional user metadata here if needed
        }
      }
    });

    if (error) {
      console.error("Signup error:", error);
      return res.status(400).json({
        error: error.message,
      });
    }

    res.json({
      success: true,
      session: data.session,
      user: data.user,
    });
  } catch (error: any) {
    console.error("Signup error:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// POST /api/auth/logout
router.post("/logout", async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        error: "Authorization header required",
      });
    }

    const token = authHeader.split(" ")[1];

    // Create a temporary client with the user's token
    const { error } = await supabaseClient.auth.admin.signOut(token);

    if (error) {
      console.error("Logout error:", error);
      // Don't fail logout if there's an error - the token might already be invalid
    }

    res.json({
      success: true,
    });
  } catch (error: any) {
    console.error("Logout error:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// GET /api/auth/session
router.get("/session", async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({
        error: "Authorization header required",
      });
    }

    const token = authHeader.split(" ")[1];

    const { data, error } = await supabaseClient.auth.getUser(token);

    if (error || !data.user) {
      return res.status(401).json({
        error: "Invalid or expired token",
      });
    }

    res.json({
      success: true,
      user: data.user,
    });
  } catch (error: any) {
    console.error("Session validation error:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// POST /api/auth/refresh
router.post("/refresh", async (req: Request, res: Response) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({
        error: "Refresh token is required",
      });
    }

    const { data, error } = await supabaseClient.auth.refreshSession({
      refresh_token,
    });

    if (error) {
      console.error("Token refresh error:", error);
      return res.status(401).json({
        error: error.message,
      });
    }

    if (!data.session || !data.user) {
      return res.status(401).json({
        error: "Token refresh failed",
      });
    }

    res.json({
      success: true,
      session: data.session,
      user: data.user,
    });
  } catch (error: any) {
    console.error("Token refresh error:", error);
    res.status(500).json({
      error: "Internal server error",
    });
  }
});

// GET /api/auth/confirm - Handle email confirmation callback
router.get("/confirm", async (req: Request, res: Response) => {
  try {
    const { token_hash, type } = req.query;

    if (!token_hash || !type) {
      return res.status(400).json({
        error: "Missing token_hash or type parameter",
      });
    }

    // Verify the email confirmation token
    const { data, error } = await supabaseClient.auth.verifyOtp({
      token_hash: token_hash as string,
      type: type as any, // 'email' or 'signup'
    });

    if (error) {
      console.error("Email confirmation error:", error);
      // Redirect to frontend with error
      return res.redirect(`/?error=${encodeURIComponent(error.message)}`);
    }

    if (data.session) {
      // Successful confirmation - redirect to frontend with success
      return res.redirect(`/?confirmed=true`);
    } else {
      // No session but no error - user might already be confirmed
      return res.redirect(`/?message=${encodeURIComponent('Email confirmed successfully')}`);
    }

  } catch (error: any) {
    console.error("Email confirmation error:", error);
    return res.redirect(`/?error=${encodeURIComponent('Confirmation failed')}`);
  }
});

export default router;
