import React, { useState, useEffect } from "react";
import { useParams, useLocation } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import { QuizQuestionManager } from "@/components/quiz/QuizQuestionManager";
import { ErrorBoundary } from "@/components/common/ErrorBoundary";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "@/hooks/useAuth";
import { Tables } from "@/types/supabase";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertCircle, ArrowLeft } from "lucide-react";
import Spinner from "@/components/ui/Spinner";

type Quiz = Tables<"quizzes">;

const QuizEditPage: React.FC = () => {
  const params = useParams<{ quizId: string }>();
  const [, navigate] = useLocation();
  const { user, signOut } = useAuth();
  const quizId = params?.quizId;

  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchQuiz = async () => {
      if (!user || !quizId) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Fetch quiz via backend API
        const token = localStorage.getItem('auth_token');
        const response = await fetch(`/api/quizzes/${quizId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          if (response.status === 401) {
            // Authentication failed - sign out and redirect
            console.warn('Authentication failed in QuizEditPage, signing out...');
            try {
              await signOut();
            } catch (signOutError) {
              console.error('Error during signOut:', signOutError);
            }
            navigate('/');
            return;
          } else if (response.status === 404) {
            setError("Quiz not found or you don't have permission to edit it.");
          } else {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || 'Failed to fetch quiz');
          }
        } else {
          const quizData = await response.json();
          setQuiz(quizData);
        }
      } catch (err: any) {
        console.error("Error fetching quiz:", err);
        setError(err.message || "Failed to load quiz details.");
      } finally {
        setLoading(false);
      }
    };

    fetchQuiz();
  }, [user, quizId]);

  const handleClose = () => {
    navigate("/quizzes");
  };

  if (loading) {
    return (
      <AppLayout title="Loading Quiz...">
        <div className="flex justify-center items-center py-12">
          <Spinner size="lg" />
          <span className="ml-4 text-xl text-slate-200">Loading quiz...</span>
        </div>
      </AppLayout>
    );
  }

  if (error || !quiz) {
    return (
      <AppLayout title="Quiz Not Found">
        <div className="container mx-auto px-4 py-8">
          <Card className="bg-slate-800 border-slate-700 max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-200 mb-2">
                Quiz Not Found
              </h3>
              <p className="text-slate-400 mb-4">
                {error || "The quiz you're looking for doesn't exist or you don't have permission to edit it."}
              </p>
              <Button
                onClick={handleClose}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quizzes
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout title={`Edit: ${quiz.name}`}>
      <div className="container mx-auto px-4 py-8">
        <ErrorBoundary>
          <QuizQuestionManager
            selectedQuizId={quiz.id}
            selectedQuizName={quiz.name}
            studyDocumentId={quiz.study_document_id || undefined}
            // This prop might not be used by QuizQuestionManager directly, but good to pass if needed for sub-components or future use
            onClose={handleClose}
          />
        </ErrorBoundary>
      </div>
    </AppLayout>
  );
};

export default QuizEditPage; 