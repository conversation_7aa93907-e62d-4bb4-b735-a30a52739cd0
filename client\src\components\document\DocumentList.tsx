import React, { useEffect, useState, useCallback } from "react";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { Button } from "@/components/ui/button";
import Spinner from "@/components/ui/Spinner"; // Import Spinner
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"; // Using Card for consistency
import { useLocation } from "wouter";
import { FlashcardGenerationPopup } from "../flashcards/FlashcardGenerationPopup";
import { DashboardQuizGenerationPopup } from "../quiz/DashboardQuizGenerationPopup";
import { API_BASE_URL } from "@/lib/config";

type StudyDocument = Tables<"study_documents">;

interface DocumentListProps {
  onSelectDocument: (document: StudyDocument) => void;
  onGenerateQuiz: (document: StudyDocument, numberOfQuestions?: number, customPrompt?: string) => void;
  onGenerateFlashcards: (document: StudyDocument, numberOfCards?: number, customPrompt?: string) => void;
}

export const DocumentList: React.FC<DocumentListProps> = ({
  onSelectDocument,
  onGenerateQuiz,
  onGenerateFlashcards,
}) => {
  const { user } = useAuth();
  const [, navigate] = useLocation();
  const [documents, setDocuments] = useState<StudyDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [generatingQuizForDoc, setGeneratingQuizForDoc] = useState<string | null>(null);
  const [generatingFlashcardsForDoc, setGeneratingFlashcardsForDoc] = useState<string | null>(null);

  const fetchDocuments = useCallback(async () => {
    if (!user) {
      setLoading(false);
      setDocuments([]); // Clear documents if no user
      return;
    }
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE_URL}/documents`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setDocuments(data || []);
    } catch (err: any) {
      console.error("Error fetching documents:", err);
      setError(err.message || "Failed to fetch documents.");
      setDocuments([]); // Clear documents on error
    } finally {
      setLoading(false);
    }
  }, [user]);

  useEffect(() => {
    fetchDocuments();
    // Real-time updates removed - using periodic refresh or manual refresh
  }, [user, fetchDocuments]);

  const handleDeleteDocument = async (
    documentId: string,
    filePath: string | null
  ) => {
    if (!user) return;
    if (
      !window.confirm(
        "Are you sure you want to delete this document? This may also delete associated flashcards and quizzes if database cascades are set up."
      )
    )
      return;

    try {
      const token = localStorage.getItem('auth_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE_URL}/documents/${documentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      // Manually remove from state since we don't have real-time updates
      setDocuments(documents.filter((doc) => doc.id !== documentId));

      alert("Document deleted successfully.");
    } catch (err: any) {
      console.error("Error deleting document:", err);
      alert(`Failed to delete document: ${err.message}`);
    }
  };

  const handleQuizGeneration = async (
    document: StudyDocument,
    options: { numberOfQuestions: number; customPrompt: string }
  ) => {
    setGeneratingQuizForDoc(document.id);
    try {
      await onGenerateQuiz(document, options.numberOfQuestions, options.customPrompt);
    } catch (error) {
      console.error("Quiz generation failed:", error);
    } finally {
      setGeneratingQuizForDoc(null);
    }
  };

  const handleFlashcardGeneration = async (
    document: StudyDocument,
    options: { numberOfCards: number; customPrompt: string }
  ) => {
    setGeneratingFlashcardsForDoc(document.id);
    try {
      await onGenerateFlashcards(document, options.numberOfCards, options.customPrompt);
    } catch (error) {
      console.error("Flashcard generation failed:", error);
    } finally {
      setGeneratingFlashcardsForDoc(null);
    }
  };

  if (loading && documents.length === 0) {
    return (
      <div className="flex justify-center items-center py-10">
        <Spinner size="md" />
      </div>
    );
  }

  if (error) {
    return <p className="text-center text-destructive py-4">Error: {error}</p>;
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-10">
        <span className="material-icons text-6xl text-muted-foreground/50 mb-4 block">
          description
        </span>
        <p className="text-muted-foreground mb-2">No study documents found.</p>
        <p className="text-sm text-muted-foreground/80">
          Upload a document to get started.
        </p>
        {/* Optionally, add an upload button here if not handled by DocumentUploadForm above */}
      </div>
    );
  }

  return (
    // The Card component is used in DashboardPage.tsx which wraps this list.
    // This component will just render the list structure.
    <div className="mt-6">
      {/* The title "My Uploaded Documents" is now part of the parent CardHeader in DashboardPage */}
      <ul className="space-y-4">
        {documents.map((doc) => (
          <li
            key={doc.id}
            className="bg-card border border-border rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-200 ease-in-out hover:border-primary/20"
          >
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <div className="flex-grow mb-3 sm:mb-0">
                <h4
                  className="font-semibold text-lg text-foreground hover:text-purple-400 cursor-pointer transition-colors"
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  title="Click to open in full view"
                >
                  {doc.file_name || "Unnamed Document"}
                </h4>
                <div className="text-xs text-muted-foreground space-y-0.5 mt-1">
                  <p>Type: {doc.content_type || "N/A"}</p>
                  <p>
                    Size:{" "}
                    {doc.size_bytes
                      ? (doc.size_bytes / 1024).toFixed(2)
                      : "N/A"}{" "}
                    KB
                  </p>
                  <p>
                    Status:{" "}
                    <span
                      className={`font-medium ${
                        doc.status === "extracted"
                          ? "text-success"
                          : "text-warning"
                      }`}
                    >
                      {doc.status || "Pending"}
                    </span>
                  </p>
                  <p>
                    Uploaded: {new Date(doc.created_at!).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 items-center justify-start sm:justify-end w-full sm:w-auto shrink-0">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onSelectDocument(doc)}
                  aria-label={`View ${doc.file_name}`}
                  className="bg-background hover:bg-accent hover:text-accent-foreground border-border"
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    visibility
                  </span>
                  <span className="hidden sm:inline">View</span>
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/documents/${doc.id}`)}
                  aria-label={`Open ${doc.file_name} in full view`}
                  className="bg-background hover:bg-primary/10 hover:text-primary border-primary/30 text-primary"
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    open_in_new
                  </span>
                  <span className="hidden sm:inline">Full View</span>
                </Button>
                <DashboardQuizGenerationPopup
                  trigger={
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={doc.status !== "extracted" || generatingQuizForDoc === doc.id}
                      title={
                        doc.status !== "extracted"
                          ? "Text extraction pending or failed for quiz generation"
                          : "Generate Quiz"
                      }
                      aria-label={`Generate quiz for ${doc.file_name}`}
                      className="bg-background hover:bg-accent hover:text-accent-foreground border-border"
                    >
                      <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                        quiz
                      </span>
                      <span className="hidden sm:inline">
                        {generatingQuizForDoc === doc.id ? "Generating..." : "Quiz"}
                      </span>
                    </Button>
                  }
                  onGenerate={(options) => handleQuizGeneration(doc, options)}
                  isGenerating={generatingQuizForDoc === doc.id}
                  disabled={doc.status !== "extracted" || generatingQuizForDoc === doc.id}
                  maxQuestions={20}
                />
                <FlashcardGenerationPopup
                  trigger={
                    <Button
                      variant="outline"
                      size="sm"
                      disabled={doc.status !== "extracted" || generatingFlashcardsForDoc === doc.id}
                      title={
                        doc.status !== "extracted"
                          ? "Text extraction pending or failed for flashcard generation"
                          : "Generate Flashcards"
                      }
                      aria-label={`Generate flashcards for ${doc.file_name}`}
                      className="bg-background hover:bg-accent hover:text-accent-foreground border-border"
                    >
                      <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                        style
                      </span>
                      <span className="hidden sm:inline">
                        {generatingFlashcardsForDoc === doc.id ? "Generating..." : "Cards"}
                      </span>
                    </Button>
                  }
                  onGenerate={(options) => handleFlashcardGeneration(doc, options)}
                  isGenerating={generatingFlashcardsForDoc === doc.id}
                  disabled={doc.status !== "extracted" || generatingFlashcardsForDoc === doc.id}
                  maxCards={50}
                />
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteDocument(doc.id, doc.file_path)}
                  aria-label={`Delete ${doc.file_name}`}
                >
                  <span className="material-icons md-18 mr-1 sm:mr-0 md:mr-1">
                    delete
                  </span>
                  <span className="hidden sm:inline">Delete</span>
                </Button>
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};
