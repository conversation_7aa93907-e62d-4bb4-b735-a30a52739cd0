import React from "react";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { KeyRound } from "lucide-react";

interface OpenRouterKeyGuideDialogProps {
  trigger?: React.ReactNode;
}

const OpenRouterKeyGuideDialog: React.FC<OpenRouterKeyGuideDialogProps> = ({ trigger }) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="link" className="p-0 text-purple-400 hover:text-purple-300">
            How do I get an OpenRouter API key?
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md bg-slate-800 border-slate-700 text-slate-200">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-purple-400">
            <KeyRound className="h-5 w-5" /> Get Your OpenRouter API Key
          </DialogTitle>
          <DialogDescription className="text-slate-300">
            Follow these quick steps:
          </DialogDescription>
        </DialogHeader>
        <ol className="list-decimal list-inside space-y-2 mt-4 text-slate-300">
          <li>
            Visit <a href="https://openrouter.ai" target="_blank" rel="noopener" className="text-purple-300 underline">openrouter.ai</a>
            {" "}and sign up or log in.
          </li>
          <li>Click your profile icon and choose <strong>"Keys"</strong>.</li>
          <li>Press <strong>"Create Key"</strong> and name it <em>ChewyAI</em>.</li>
          <li>Click <strong>"Create"</strong> and copy the secret key shown.</li>
          <li>Return here and paste the key into the <strong>API Key</strong> field.</li>
        </ol>
      </DialogContent>
    </Dialog>
  );
};

export default OpenRouterKeyGuideDialog;
