import React, { useEffect, useState } from "react";
// Removed direct Supabase import - using backend API endpoints
import { useAuth } from "../../hooks/useAuth";
import { Tables } from "../../types/supabase";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  ChevronLeft,
  ChevronRight,
  X,
  Clock,
  CheckCircle,
  AlertCircle,
  Trophy,
  RotateCcw,
} from "lucide-react";
import { notify } from "@/lib/notifications";
import { useKeyboardNavigation } from "@/hooks/useKeyboardNavigation";
import { useQuizSettings } from "@/contexts/QuizSettingsContext";
import { QuizSettingsToggle } from "./QuizSettingsToggle";
import {
  ReviewDifficulty,
  updateQuizQuestionSRS,
  sortQuizQuestionsByDueDate,
  getQuizQuestionsDueCount,
} from "@/lib/srs";
import { QuizAPI } from "@/lib/api/quizApi";
type Quiz = Tables<"quizzes">;
type QuizQuestion = Tables<"quiz_questions">;
// Ensure McqOption type is defined if you use it for displaying options
// interface McqOption { text: string; is_correct: boolean; id?: string; }

interface QuizPlayerProps {
  quizId: string;
  onQuizComplete?: (score: number, totalQuestions: number) => void; // Optional callback
  onExit: () => void; // Callback to close the player
}

interface QuizResults {
  score: number;
  totalQuestions: number;
  percentage: number;
  timeSpent: number;
}

export const QuizPlayer: React.FC<QuizPlayerProps> = ({
  quizId,
  onQuizComplete,
  onExit,
}) => {
  const { user } = useAuth();
  const { settings } = useQuizSettings();
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [userAnswers, setUserAnswers] = useState<Record<string, any>>({}); // { questionId: answer }
  const [submittedAnswers, setSubmittedAnswers] = useState<Array<boolean>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [results, setResults] = useState<QuizResults | null>(null);
  const [startTime] = useState(Date.now());

  useEffect(() => {
    const fetchQuizData = async () => {
      if (!user || !quizId) return;
      setLoading(true);
      setError(null);
      try {
        // Fetch quiz details
        // Fetch quiz data from backend API
        const token = localStorage.getItem('auth_token');
        const quizResponse = await fetch(`/api/quizzes/${quizId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!quizResponse.ok) {
          throw new Error('Failed to fetch quiz data');
        }

        const quizData = await quizResponse.json();
        setQuiz(quizData);

        // Fetch quiz questions from backend API
        const questionsResponse = await fetch(`/api/quizzes/${quizId}/questions`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!questionsResponse.ok) {
          throw new Error('Failed to fetch quiz questions');
        }

        const questionsData = await questionsResponse.json();

        // Sort questions by SRS due date for optimal spaced repetition
        const sortedQuestions = sortQuizQuestionsByDueDate(questionsData || []);
        setQuestions(sortedQuestions);
        setSubmittedAnswers(new Array(sortedQuestions.length).fill(false));
      } catch (err: any) {
        console.error("Error fetching quiz data:", err);
        setError(err.message || "Failed to load quiz.");
        notify.error({
          title: "Failed to load quiz",
          description: err.message || "Please try again later.",
        });
      } finally {
        setLoading(false);
      }
    };
    fetchQuizData();
  }, [quizId, user]);

  const handleAnswerSelect = (questionId: string, answer: any) => {
    setUserAnswers((prev) => ({ ...prev, [questionId]: answer }));
  };

  const handleSelectAllAnswerToggle = (
    questionId: string,
    optionText: string
  ) => {
    setUserAnswers((prev) => {
      const currentAnswers = prev[questionId] || [];
      const isSelected = currentAnswers.includes(optionText);

      if (isSelected) {
        return {
          ...prev,
          [questionId]: currentAnswers.filter(
            (ans: string) => ans !== optionText
          ),
        };
      } else {
        return {
          ...prev,
          [questionId]: [...currentAnswers, optionText],
        };
      }
    });
  };

  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const checkAnswerCorrect = (
    question: QuizQuestion,
    userAnswer: any
  ): boolean => {
    if (!userAnswer) return false;

    if (question.type === "multiple_choice" && question.options) {
      const selectedOption = (question.options as any[])?.find(
        (opt) => opt.text === userAnswer
      );
      return selectedOption?.is_correct || false;
    } else if (question.type === "select_all_that_apply" && question.options) {
      const userSelectedAnswers = userAnswer || [];
      const correctOptions = (question.options as any[])?.filter(
        (opt) => opt.is_correct
      );
      const correctOptionTexts = correctOptions.map((opt) => opt.text);

      return (
        correctOptionTexts.length === userSelectedAnswers.length &&
        correctOptionTexts.every((text) => userSelectedAnswers.includes(text))
      );
    } else if (question.type === "true_false") {
      return userAnswer === question.correct_answer;
    } else if (question.type === "short_answer") {
      return (
        userAnswer?.toLowerCase().trim() ===
        question.correct_answer?.toLowerCase().trim()
      );
    }
    return false;
  };
  const handleSubmitAnswer = async () => {
    const currentQuestion = questions[currentQuestionIndex];
    if (
      currentQuestion &&
      userAnswers[currentQuestion.id] !== undefined &&
      !submittedAnswers[currentQuestionIndex]
    ) {
      const isCorrect = checkAnswerCorrect(
        currentQuestion,
        userAnswers[currentQuestion.id]
      );
      const newSubmittedAnswers = [...submittedAnswers];
      newSubmittedAnswers[currentQuestionIndex] = true;
      setSubmittedAnswers(newSubmittedAnswers);

      // SRS Update Logic
      const difficulty = isCorrect
        ? ReviewDifficulty.EASY
        : ReviewDifficulty.DIFFICULT; // Simplified difficulty
      const updatedQuestion = updateQuizQuestionSRS(
        currentQuestion,
        difficulty
      );

      // Persist SRS updates to the backend
      try {
        await QuizAPI.updateQuestionSRS(updatedQuestion.id, {
          srs_level: updatedQuestion.srs_level || undefined,
          due_at: updatedQuestion.due_at || undefined,
          last_reviewed_at: updatedQuestion.last_reviewed_at || undefined,
          srs_interval: updatedQuestion.srs_interval || undefined,
          srs_ease_factor: updatedQuestion.srs_ease_factor || undefined,
          srs_repetitions: updatedQuestion.srs_repetitions || undefined,
          srs_correct_streak: updatedQuestion.srs_correct_streak || undefined,
        });
        console.log("SRS Updated successfully:", updatedQuestion.id);
      } catch (error) {
        console.error("Failed to update SRS data:", error);
        notify.error({
          title: "SRS Update Failed",
          description: "Failed to save spaced repetition data",
        });
      }

      // Auto-advance to next question if enabled
      if (settings.autoAdvance) {
        setTimeout(() => {
          if (currentQuestionIndex < questions.length - 1) {
            goToNextQuestion();
          } else {
            handleSubmitQuiz();
          }
        }, 1500);
      }
    }
  };

  const calculateScore = (): QuizResults => {
    let score = 0;
    const timeSpent = Math.round((Date.now() - startTime) / 1000);

    questions.forEach((q) => {
      const userAnswer = userAnswers[q.id];
      if (!userAnswer) return;

      if (q.type === "multiple_choice" && q.options) {
        const selectedOption = (q.options as any[])?.find(
          (opt) => opt.text === userAnswer
        );
        if (selectedOption?.is_correct) {
          score++;
        }
      } else if (q.type === "select_all_that_apply" && q.options) {
        const userSelectedAnswers = userAnswer || [];
        const correctOptions = (q.options as any[])?.filter(
          (opt) => opt.is_correct
        );
        const correctOptionTexts = correctOptions.map((opt) => opt.text);

        // Check if user selected exactly the correct answers (no more, no less)
        const isCorrect =
          correctOptionTexts.length === userSelectedAnswers.length &&
          correctOptionTexts.every((text) =>
            userSelectedAnswers.includes(text)
          );

        if (isCorrect) {
          score++;
        }
      } else if (q.type === "true_false") {
        if (userAnswer === q.correct_answer) {
          score++;
        }
      } else if (q.type === "short_answer") {
        if (
          userAnswer?.toLowerCase().trim() ===
          q.correct_answer?.toLowerCase().trim()
        ) {
          score++;
        }
      }
    });

    const percentage = Math.round((score / questions.length) * 100);

    return {
      score,
      totalQuestions: questions.length,
      percentage,
      timeSpent,
    };
  };

  const handleSubmitQuiz = () => {
    const quizResults = calculateScore();
    setResults(quizResults);
    setIsCompleted(true);

    if (quizResults.percentage >= 70) {
      notify.success({
        title: "Great job!",
        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%)`,
      });
    } else {
      notify.info({
        title: "Quiz completed",
        description: `You scored ${quizResults.score}/${quizResults.totalQuestions} (${quizResults.percentage}%). Keep studying!`,
      });
    }

    onQuizComplete?.(quizResults.score, quizResults.totalQuestions);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const restartQuiz = () => {
    setCurrentQuestionIndex(0);
    setUserAnswers({});
    setSubmittedAnswers(new Array(questions.length).fill(false));
    setIsCompleted(false);
    setResults(null);
  };

  // Keyboard navigation
  useKeyboardNavigation({
    onNext: () =>
      !isCompleted &&
      currentQuestionIndex < questions.length - 1 &&
      goToNextQuestion(),
    onPrevious: () =>
      !isCompleted && currentQuestionIndex > 0 && goToPreviousQuestion(),
    onSubmit: () =>
      !isCompleted &&
      currentQuestionIndex === questions.length - 1 &&
      handleSubmitQuiz(),
    onEscape: onExit,
    disabled: loading || isCompleted,
  });

  if (loading) {
    return (
      <Card className="bg-slate-800 border-slate-700 max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <p className="text-slate-300">Loading quiz...</p>
        </CardContent>
      </Card>
    );
  }

  if (error || !quiz) {
    return (
      <Card className="bg-slate-800 border-slate-700 max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2">
            {error ? "Error Loading Quiz" : "Quiz Not Found"}
          </h3>
          <p className="text-slate-400 mb-4">
            {error || "The quiz you're looking for doesn't exist."}
          </p>
          <Button
            onClick={onExit}
            variant="outline"
            className="border-slate-600 text-slate-300"
          >
            Go Back
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (questions.length === 0) {
    return (
      <Card className="bg-slate-800 border-slate-700 max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-200 mb-2">
            No Questions
          </h3>
          <p className="text-slate-400 mb-4">
            This quiz doesn't have any questions yet.
          </p>
          <Button
            onClick={onExit}
            variant="outline"
            className="border-slate-600 text-slate-300"
          >
            Go Back
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (isCompleted && results) {
    return (
      <Card className="bg-slate-800 border-slate-700 max-w-2xl mx-auto">
        <CardContent className="p-8 text-center">
          <Trophy
            className={`h-16 w-16 mx-auto mb-4 ${
              results.percentage >= 70 ? "text-yellow-400" : "text-slate-400"
            }`}
          />

          <h2 className="text-2xl font-semibold text-slate-200 mb-2">
            Quiz Complete!
          </h2>
          <p className="text-slate-400 mb-6">Here are your results:</p>

          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-slate-900 p-4 rounded-lg">
              <p className="text-slate-400 text-sm">Score</p>
              <p className="text-2xl font-bold text-purple-400">
                {results.score}/{results.totalQuestions}
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg">
              <p className="text-slate-400 text-sm">Percentage</p>
              <p className="text-2xl font-bold text-purple-400">
                {results.percentage}%
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg">
              <p className="text-slate-400 text-sm">Time</p>
              <p className="text-2xl font-bold text-purple-400">
                {formatTime(results.timeSpent)}
              </p>
            </div>
            <div className="bg-slate-900 p-4 rounded-lg">
              <p className="text-slate-400 text-sm">Grade</p>
              <Badge
                variant={results.percentage >= 70 ? "default" : "secondary"}
                className="text-lg px-3 py-1"
              >
                {results.percentage >= 90
                  ? "A"
                  : results.percentage >= 80
                  ? "B"
                  : results.percentage >= 70
                  ? "C"
                  : "D"}
              </Badge>
            </div>
          </div>

          <div className="flex justify-center space-x-3">
            <Button
              onClick={restartQuiz}
              variant="outline"
              className="border-slate-600 text-slate-300"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Retry
            </Button>
            <Button
              onClick={onExit}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Finish
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
  const dueQuestionsCount = getQuizQuestionsDueCount(questions);

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold text-slate-200">
                {quiz.name}
              </h2>
              {quiz.description && (
                <p className="text-slate-400 text-sm mt-1">
                  {quiz.description}
                </p>
              )}
            </div>
            <div className="flex items-center gap-2">
              <QuizSettingsToggle size="sm" />
              <Button
                onClick={onExit}
                variant="ghost"
                size="sm"
                className="text-slate-400 hover:text-slate-200"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-slate-400">
                Question {currentQuestionIndex + 1} of {questions.length}
              </span>
              {dueQuestionsCount > 0 && (
                <Badge
                  variant="secondary"
                  className="text-xs bg-orange-900/30 text-orange-400 border-orange-700"
                >
                  {dueQuestionsCount} due for review
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2 text-sm text-slate-400">
              <Clock className="h-4 w-4" />
              <span>
                {formatTime(Math.round((Date.now() - startTime) / 1000))}
              </span>
            </div>
          </div>

          <Progress value={progress} className="mb-6" />
        </CardContent>
      </Card>

      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <h3 className="text-lg font-medium text-slate-200 mb-4">
            {currentQuestion.question_text}
          </h3>

          <div className="space-y-3">            {currentQuestion.type === "multiple_choice" &&
              currentQuestion.options && (
                <div className="space-y-3">
                  {(currentQuestion.options as any[]).map((option, index) => {
                    const isSelected = userAnswers[currentQuestion.id] === option.text;
                    const isSubmitted = submittedAnswers[currentQuestionIndex];
                    
                    // Determine border and background colors based on feedback settings
                    let borderClass = "border-slate-600";
                    let bgClass = "";
                    
                    if (isSelected) {
                      if (isSubmitted && settings.instantFeedback) {
                        // Show correct/incorrect feedback
                        if (option.is_correct) {
                          borderClass = "border-green-500";
                          bgClass = "bg-green-950/20";
                        } else {
                          borderClass = "border-red-500";
                          bgClass = "bg-red-950/20";
                        }
                      } else {
                        // Default selection style
                        borderClass = "border-purple-500";
                        bgClass = "bg-purple-950/20";
                      }
                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {
                      // Highlight correct answer even if not selected
                      borderClass = "border-green-500";
                      bgClass = "bg-green-950/20";
                    }

                    return (
                      <label
                        key={index}
                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}
                      >
                        <input
                          type="radio"
                          name={`question-${currentQuestion.id}`}
                          value={option.text}
                          checked={isSelected}
                          onChange={() =>
                            !isSubmitted && handleAnswerSelect(currentQuestion.id, option.text)
                          }
                          className="sr-only"
                          disabled={isSubmitted}
                        />
                        <div
                          className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                            isSelected
                              ? isSubmitted && settings.instantFeedback
                                ? option.is_correct
                                  ? "border-green-500 bg-green-500"
                                  : "border-red-500 bg-red-500"
                                : "border-purple-500 bg-purple-500"
                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct
                              ? "border-green-500 bg-green-500"
                              : "border-slate-400"
                          }`}
                        >
                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (
                            <div className="w-2 h-2 rounded-full bg-white"></div>
                          )}
                        </div>
                        <span className="text-slate-200">{option.text}</span>
                      </label>
                    );
                  })}
                </div>
              )}            {currentQuestion.type === "select_all_that_apply" &&
              currentQuestion.options && (
                <div className="space-y-3">
                  <p className="text-sm text-slate-400 mb-3 italic">
                    Select all correct answers:
                  </p>
                  {(currentQuestion.options as any[]).map((option, index) => {
                    const isSelected = (
                      userAnswers[currentQuestion.id] || []
                    ).includes(option.text);
                    const isSubmitted = submittedAnswers[currentQuestionIndex];
                    
                    // Determine border and background colors based on feedback settings
                    let borderClass = "border-slate-600";
                    let bgClass = "";
                    
                    if (isSelected) {
                      if (isSubmitted && settings.instantFeedback) {
                        // Show correct/incorrect feedback for selected options
                        if (option.is_correct) {
                          borderClass = "border-green-500";
                          bgClass = "bg-green-950/20";
                        } else {
                          borderClass = "border-red-500";
                          bgClass = "bg-red-950/20";
                        }
                      } else {
                        // Default selection style
                        borderClass = "border-purple-500";
                        bgClass = "bg-purple-950/20";
                      }
                    } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct) {
                      // Highlight unselected correct answers
                      borderClass = "border-green-500";
                      bgClass = "bg-green-950/20";
                    }

                    return (
                      <label
                        key={index}
                        className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}
                      >
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() =>
                            !isSubmitted && handleSelectAllAnswerToggle(
                              currentQuestion.id,
                              option.text
                            )
                          }
                          className="sr-only"
                          disabled={isSubmitted}
                        />
                        <div
                          className={`w-4 h-4 border-2 mr-3 flex items-center justify-center rounded ${
                            isSelected
                              ? isSubmitted && settings.instantFeedback
                                ? option.is_correct
                                  ? "border-green-500 bg-green-500"
                                  : "border-red-500 bg-red-500"
                                : "border-purple-500 bg-purple-500"
                              : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct
                              ? "border-green-500 bg-green-500"
                              : "border-slate-400"
                          }`}
                        >
                          {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && option.is_correct)) && (
                            <div className="w-2 h-2 bg-white rounded-sm"></div>
                          )}
                        </div>
                        <span className="text-slate-200">{option.text}</span>
                      </label>
                    );
                  })}
                </div>
              )}            {currentQuestion.type === "true_false" && (
              <div className="space-y-3">
                {["True", "False"].map((val) => {
                  const isSelected = userAnswers[currentQuestion.id] === val;
                  const isSubmitted = submittedAnswers[currentQuestionIndex];
                  
                  // Determine styling based on feedback settings
                  let borderClass = "border-slate-600";
                  let bgClass = "";
                  
                  if (isSelected) {
                    if (isSubmitted && settings.instantFeedback) {
                      // Show correct/incorrect feedback
                      if (val === currentQuestion.correct_answer) {
                        borderClass = "border-green-500";
                        bgClass = "bg-green-950/20";
                      } else {
                        borderClass = "border-red-500";
                        bgClass = "bg-red-950/20";
                      }
                    } else {
                      // Default selection style
                      borderClass = "border-purple-500";
                      bgClass = "bg-purple-950/20";
                    }
                  } else if (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer) {
                    // Highlight correct answer even if not selected
                    borderClass = "border-green-500";
                    bgClass = "bg-green-950/20";
                  }

                  return (
                    <label
                      key={val}
                      className={`flex items-center p-4 rounded-lg border-2 cursor-pointer transition-all hover:bg-slate-700 ${borderClass} ${bgClass}`}
                    >
                      <input
                        type="radio"
                        name={`question-${currentQuestion.id}`}
                        value={val}
                        checked={isSelected}
                        onChange={() =>
                          !isSubmitted && handleAnswerSelect(currentQuestion.id, val)
                        }
                        className="sr-only"
                        disabled={isSubmitted}
                      />
                      <div
                        className={`w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center ${
                          isSelected
                            ? isSubmitted && settings.instantFeedback
                              ? val === currentQuestion.correct_answer
                                ? "border-green-500 bg-green-500"
                                : "border-red-500 bg-red-500"
                              : "border-purple-500 bg-purple-500"
                            : isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer
                            ? "border-green-500 bg-green-500"
                            : "border-slate-400"
                        }`}
                      >
                        {(isSelected || (isSubmitted && settings.instantFeedback && settings.showCorrectAnswers && val === currentQuestion.correct_answer)) && (
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        )}
                      </div>
                      <span className="text-slate-200">{val}</span>
                    </label>
                  );
                })}
              </div>
            )}            {currentQuestion.type === "short_answer" && (
              <div className="space-y-3">
                <input
                  type="text"
                  value={userAnswers[currentQuestion.id] || ""}
                  onChange={(e) =>
                    !submittedAnswers[currentQuestionIndex] && handleAnswerSelect(currentQuestion.id, e.target.value)
                  }
                  className={`w-full px-4 py-3 border rounded-lg text-slate-200 placeholder-slate-400 focus:ring-1 outline-none ${
                    submittedAnswers[currentQuestionIndex] && settings.instantFeedback
                      ? userAnswers[currentQuestion.id]
                          ?.toLowerCase()
                          .trim() ===
                        currentQuestion.correct_answer?.toLowerCase().trim()
                        ? "bg-green-950/20 border-green-500 focus:border-green-500 focus:ring-green-500"
                        : "bg-red-950/20 border-red-500 focus:border-red-500 focus:ring-red-500"
                      : "bg-slate-900 border-slate-600 focus:border-purple-500 focus:ring-purple-500"
                  }`}
                  placeholder="Type your answer here..."
                  disabled={submittedAnswers[currentQuestionIndex]}
                />
                {submittedAnswers[currentQuestionIndex] && settings.instantFeedback && settings.showCorrectAnswers && (
                  <p className="text-sm text-slate-400">
                    <span className="font-medium">Correct answer:</span>{" "}
                    {currentQuestion.correct_answer}
                  </p>
                )}              </div>
            )}
          </div>

          {submittedAnswers[currentQuestionIndex] && settings.showExplanations && currentQuestion.explanation && (
            <div className="mt-4 p-4 bg-slate-900 rounded-lg border border-slate-600">
              <h4 className="text-sm font-medium text-slate-300 mb-2">
                Explanation:
              </h4>
              <p className="text-slate-400 text-sm">
                {currentQuestion.explanation}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-between items-center">
        <Button
          onClick={goToPreviousQuestion}
          disabled={currentQuestionIndex === 0}
          variant="outline"
          className="border-slate-600 text-slate-300 disabled:opacity-50"
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Previous
        </Button>

        <div className="flex gap-2">
          {!submittedAnswers[currentQuestionIndex] &&
          userAnswers[currentQuestion.id] !== undefined ? (
            <Button
              onClick={handleSubmitAnswer}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Submit Answer
            </Button>
          ) : submittedAnswers[currentQuestionIndex] ? (
            currentQuestionIndex === questions.length - 1 ? (
              <Button
                onClick={handleSubmitQuiz}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Submit Quiz
              </Button>
            ) : (
              <Button
                onClick={goToNextQuestion}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                Next
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )
          ) : null}
        </div>
      </div>
    </div>
  );
};
