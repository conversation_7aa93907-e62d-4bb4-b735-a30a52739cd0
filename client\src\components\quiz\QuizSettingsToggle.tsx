import React from 'react';
import { Settings } from 'lucide-react';
import { useQuizSettings } from '@/contexts/QuizSettingsContext';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

interface QuizSettingsToggleProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'ghost' | 'outline';
  showLabel?: boolean;
}

export const QuizSettingsToggle: React.FC<QuizSettingsToggleProps> = ({ 
  size = 'md', 
  variant = 'ghost',
  showLabel = false 
}) => {
  const { settings, updateSetting } = useQuizSettings();

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant={variant}
          size={size === "md" ? "default" : size}
          className="transition-all duration-200 hover:scale-105"
          aria-label="Quiz Settings"
        >
          <Settings size={iconSize} />
          {showLabel && <span className="ml-2">Quiz Settings</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 bg-slate-800 border-slate-700" align="end">
        <Card className="bg-transparent border-0 shadow-none">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-slate-200 flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-400" />
              Quiz Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="instant-feedback" className="text-slate-300 flex-1">
                <div>
                  <div className="font-medium">Instant Feedback</div>
                  <div className="text-xs text-slate-400">
                    Show correct/incorrect answers immediately
                  </div>
                </div>
              </Label>
              <Switch
                id="instant-feedback"
                checked={settings.instantFeedback}
                onCheckedChange={(checked) => updateSetting('instantFeedback', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="show-correct" className="text-slate-300 flex-1">
                <div>
                  <div className="font-medium">Show Correct Answers</div>
                  <div className="text-xs text-slate-400">
                    Highlight correct options when instant feedback is on
                  </div>
                </div>
              </Label>
              <Switch
                id="show-correct"
                checked={settings.showCorrectAnswers}
                onCheckedChange={(checked) => updateSetting('showCorrectAnswers', checked)}
                disabled={!settings.instantFeedback}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="auto-advance" className="text-slate-300 flex-1">
                <div>
                  <div className="font-medium">Auto Advance</div>
                  <div className="text-xs text-slate-400">
                    Automatically move to next question after submission
                  </div>
                </div>
              </Label>
              <Switch
                id="auto-advance"
                checked={settings.autoAdvance}
                onCheckedChange={(checked) => updateSetting('autoAdvance', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="show-explanations" className="text-slate-300 flex-1">
                <div>
                  <div className="font-medium">Show Explanations</div>
                  <div className="text-xs text-slate-400">
                    Display explanations after answering questions
                  </div>
                </div>
              </Label>
              <Switch
                id="show-explanations"
                checked={settings.showExplanations}
                onCheckedChange={(checked) => updateSetting('showExplanations', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};
