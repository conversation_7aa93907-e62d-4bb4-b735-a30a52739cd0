import express from 'express';
import request from 'supertest';
import healthRoutes from '../routes/healthRoutes';

const app = express();
app.use('/api/health', healthRoutes);

describe('GET /api/health', () => {
  it('responds with status healthy', async () => {
    const res = await request(app).get('/api/health');
    expect(res.status).toBe(200);
    expect(res.body).toMatchObject({ status: 'healthy' });
  });

  it('provides detailed health info', async () => {
    const res = await request(app).get('/api/health/detailed');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('memory');
    expect(res.body).toHaveProperty('uptime');
  });
});
